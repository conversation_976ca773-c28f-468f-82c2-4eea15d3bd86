@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 text-gray-900 antialiased;
    background-attachment: fixed;
  }

  * {
    @apply border-gray-200;
  }

  /* 自定义滚动条 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full hover:bg-gray-400;
  }
}

@layer components {
  .container-full {
    @apply w-full min-h-screen;
  }

  /* 现代化卡片样式 */
  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-glass border border-white/20 hover:shadow-medium transition-all duration-300;
  }

  .card-header {
    @apply px-6 py-5 border-b border-gray-100/50;
  }

  .card-content {
    @apply p-6;
  }

  /* 现代化按钮样式 */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-3 px-6 rounded-xl shadow-lg hover:shadow-glow transition-all duration-300 transform hover:scale-105;
  }

  .btn-secondary {
    @apply bg-white/80 backdrop-blur-sm hover:bg-white text-gray-700 font-medium py-3 px-6 rounded-xl border border-gray-200/50 hover:border-gray-300 transition-all duration-300 shadow-soft hover:shadow-medium;
  }

  /* 现代化导航项样式 */
  .nav-item {
    @apply flex items-center px-4 py-3 mx-2 text-gray-600 hover:text-gray-900 rounded-xl transition-all duration-300 relative overflow-hidden;
  }

  .nav-item::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-xl opacity-0 transition-opacity duration-300;
  }

  .nav-item:hover::before {
    @apply opacity-100;
  }

  .nav-item.active {
    @apply bg-gradient-to-r from-blue-500/20 to-indigo-500/20 text-blue-700 shadow-inner-soft;
  }

  .nav-item.active::before {
    @apply opacity-100;
  }

  /* 玻璃拟态效果 */
  .glass-card {
    @apply bg-white/25 backdrop-blur-md rounded-2xl border border-white/20 shadow-glass;
  }

  /* 渐变文字 */
  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent;
  }

  /* 浮动动画 */
  .float-animation {
    @apply animate-float;
  }

  /* 发光效果 */
  .glow-effect {
    @apply shadow-glow animate-glow;
  }

  /* 现代化输入框 */
  .modern-input {
    @apply bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50 transition-all duration-300;
  }

  /* 现代化选择框 */
  .modern-select {
    @apply bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50 transition-all duration-300;
  }
}
