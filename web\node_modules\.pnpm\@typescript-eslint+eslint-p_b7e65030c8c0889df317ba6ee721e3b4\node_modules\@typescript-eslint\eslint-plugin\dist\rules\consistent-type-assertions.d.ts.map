{"version": 3, "file": "consistent-type-assertions.d.ts", "sourceRoot": "", "sources": ["../../src/rules/consistent-type-assertions.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAY,MAAM,0BAA0B,CAAC;AAgBnE,MAAM,MAAM,UAAU,GAClB,eAAe,GACf,IAAI,GACJ,OAAO,GACP,yCAAyC,GACzC,wCAAwC,GACxC,0CAA0C,GAC1C,yCAAyC,GACzC,8BAA8B,GAC9B,+BAA+B,CAAC;AACpC,KAAK,QAAQ,GACT;IACE,cAAc,EAAE,eAAe,GAAG,IAAI,CAAC;IACvC,2BAA2B,CAAC,EAAE,OAAO,GAAG,oBAAoB,GAAG,OAAO,CAAC;IACvE,0BAA0B,CAAC,EAAE,OAAO,GAAG,oBAAoB,GAAG,OAAO,CAAC;CACvE,GACD;IACE,cAAc,EAAE,OAAO,CAAC;CACzB,CAAC;AACN,MAAM,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC;;AAM1C,wBA2TG"}