import type { InferMessageIdsTypeFromRule, InferOptionsTypeFromRule } from '../util';
import { getESLintCoreRule } from '../util/getESLintCoreRule';
declare const baseRule: ReturnType<typeof getESLintCoreRule>;
export type Options = InferOptionsTypeFromRule<NonNullable<typeof baseRule>>;
export type MessageIds = InferMessageIdsTypeFromRule<NonNullable<typeof baseRule>>;
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"noLossOfPrecision", [], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-loss-of-precision.d.ts.map