{"version": 3, "file": "no-misused-promises.d.ts", "sourceRoot": "", "sources": ["../../src/rules/no-misused-promises.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAY,MAAM,0BAA0B,CAAC;AAiBnE,MAAM,MAAM,OAAO,GAAG;IACpB;QACE,kBAAkB,CAAC,EAAE,OAAO,CAAC;QAC7B,aAAa,CAAC,EAAE,OAAO,CAAC;QACxB,gBAAgB,CAAC,EAAE,OAAO,GAAG,uBAAuB,CAAC;KACtD;CACF,CAAC;AAEF,MAAM,WAAW,uBAAuB;IACtC,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,SAAS,CAAC,EAAE,OAAO,CAAC;CACrB;AAED,MAAM,MAAM,SAAS,GACjB,aAAa,GACb,WAAW,GACX,QAAQ,GACR,oBAAoB,GACpB,qBAAqB,GACrB,2BAA2B,GAC3B,oBAAoB,GACpB,uBAAuB,GACvB,oBAAoB,CAAC;;AAgCzB,wBAgmBG"}