/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?3032\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/link.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4zMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1dvcmtzcGFjZSU1QyU1Q3l5LXpzJTVDJTVDd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTQuMi4zMF9yZWFjdC1kb20lNDAxOC4zLjFfcmVhY3QlNDAxOC4zLjFfX3JlYWN0JTQwMTguMy4xJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyKiUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ1dBQXdMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHl1bnl1L21haW4vP2ZjMzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxXb3Jrc3BhY2VcXFxceXktenNcXFxcd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFwucG5wbVxcXFxuZXh0QDE0LjIuMzBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4ec245f266f1\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHl1bnl1L21haW4vLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2U0MmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0ZWMyNDVmMjY2ZjFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"云宇政数平台\",\n    description: \"统一的政数局数据管理平台 - 大屏展示、报表分析、数据采集、数据汇聚、数据清洗治理、数据资源池管理、设备监控\",\n    keywords: \"政数局,数据平台,大屏,报表,数据采集,数据治理,设备监控\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans\",\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO0FBQ1osRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFVO3NCQUNiSjs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL0B5dW55dS9tYWluLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAn5LqR5a6H5pS/5pWw5bmz5Y+wJyxcbiAgZGVzY3JpcHRpb246ICfnu5/kuIDnmoTmlL/mlbDlsYDmlbDmja7nrqHnkIblubPlj7AgLSDlpKflsY/lsZXnpLrjgIHmiqXooajliIbmnpDjgIHmlbDmja7ph4fpm4bjgIHmlbDmja7msYfogZrjgIHmlbDmja7muIXmtJfmsrvnkIbjgIHmlbDmja7otYTmupDmsaDnrqHnkIbjgIHorr7lpIfnm5HmjqcnLFxuICBrZXl3b3JkczogJ+aUv+aVsOWxgCzmlbDmja7lubPlj7As5aSn5bGPLOaKpeihqCzmlbDmja7ph4fpm4Ys5pWw5o2u5rK755CGLOiuvuWkh+ebkeaOpycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zXCI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(rsc)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/network.js\");\n\n\n\nfunction HomePage() {\n    // 核心能力\n    const capabilities = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: \"数据全生命周期管理\",\n            description: \"从数据采集、清洗、存储到应用的全流程管理，确保数据质量和安全性\",\n            gradient: \"from-blue-500 to-cyan-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"智能数据分析\",\n            description: \"多维度数据分析和可视化展示，支持实时监控和预测分析\",\n            gradient: \"from-green-500 to-emerald-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"数据安全治理\",\n            description: \"完善的数据安全体系和质量管控机制，保障数据合规使用\",\n            gradient: \"from-purple-500 to-violet-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"跨部门数据共享\",\n            description: \"打破数据孤岛，实现政务数据统一共享和协同应用\",\n            gradient: \"from-orange-500 to-red-500\"\n        }\n    ];\n    // 解决的场景\n    const scenarios = [\n        {\n            title: \"政府决策支撑\",\n            description: \"为领导层提供实时、准确的数据分析，支撑科学决策\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            title: \"跨部门协同\",\n            description: \"消除信息孤岛，实现部门间数据无缝对接和业务协同\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            title: \"公共服务优化\",\n            description: \"通过数据分析优化公共服务流程，提升市民满意度\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            title: \"监管效能提升\",\n            description: \"利用大数据技术提升监管效率和精准度\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        }\n    ];\n    // 数据来源单位\n    const dataSourceUnits = [\n        \"市政府办公厅\",\n        \"发展改革委\",\n        \"教育局\",\n        \"科技局\",\n        \"工信局\",\n        \"公安局\",\n        \"民政局\",\n        \"司法局\",\n        \"财政局\",\n        \"人社局\",\n        \"自然资源局\",\n        \"生态环境局\",\n        \"住建局\",\n        \"交通运输局\",\n        \"水务局\",\n        \"农业农村局\",\n        \"商务局\",\n        \"文旅局\",\n        \"卫健委\",\n        \"应急管理局\",\n        \"审计局\",\n        \"市场监管局\",\n        \"统计局\",\n        \"医保局\"\n    ];\n    // 权威数据领域\n    const authorityData = [\n        {\n            area: \"人口信息\",\n            coverage: \"100%\",\n            source: \"公安、民政、人社等部门\",\n            color: \"blue\"\n        },\n        {\n            area: \"企业信息\",\n            coverage: \"100%\",\n            source: \"市场监管、税务、工信等部门\",\n            color: \"green\"\n        },\n        {\n            area: \"地理信息\",\n            coverage: \"100%\",\n            source: \"自然资源、住建、交通等部门\",\n            color: \"purple\"\n        },\n        {\n            area: \"经济数据\",\n            coverage: \"95%\",\n            source: \"统计、财政、发改等部门\",\n            color: \"orange\"\n        },\n        {\n            area: \"社会事业\",\n            coverage: \"90%\",\n            source: \"教育、卫健、文旅等部门\",\n            color: \"pink\"\n        },\n        {\n            area: \"环境数据\",\n            coverage: \"100%\",\n            source: \"生态环境、水务、应急等部门\",\n            color: \"teal\"\n        }\n    ];\n    // 目标客户\n    const targetUsers = [\n        {\n            type: \"政府决策层\",\n            desc: \"为各级领导提供数据支撑和分析报告，辅助科学决策\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            features: [\n                \"实时数据大屏\",\n                \"决策分析报告\",\n                \"趋势预测分析\"\n            ]\n        },\n        {\n            type: \"业务部门\",\n            desc: \"各委办局日常业务数据管理和跨部门协同应用\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            features: [\n                \"业务数据管理\",\n                \"跨部门协同\",\n                \"流程优化\"\n            ]\n        },\n        {\n            type: \"技术人员\",\n            desc: \"数据开发、运维和技术支持人员的专业工具\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            features: [\n                \"数据开发工具\",\n                \"系统监控\",\n                \"技术支持\"\n            ]\n        },\n        {\n            type: \"公众服务\",\n            desc: \"为市民和企业提供便民服务和信息查询\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            features: [\n                \"信息查询\",\n                \"在线服务\",\n                \"便民应用\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-indigo-400/20 to-purple-400/20 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-7xl mx-auto px-6 py-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"text-center mb-20 animate-fade-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center space-x-2 bg-blue-50/50 backdrop-blur-sm px-6 py-3 rounded-full mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-medium text-blue-700\",\n                                                children: \"智慧政务 \\xb7 数据驱动 \\xb7 创新未来\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-6xl md:text-7xl font-bold mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                            children: \"云宇政数平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed\",\n                                        children: \"统一的政务数据管理与服务平台，为政府数字化转型提供全方位的数据支撑， 实现跨部门数据共享、智能分析决策和高效政务服务\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-8 mb-12 text-lg text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"24个委办局接入\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"2.4TB数据存储\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"99.9%系统可用性\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"平台核心能力\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"为政务数字化转型提供全方位的技术支撑\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                                        children: [\n                                            capabilities.map((capability, index)=>{\n                                                const Icon = capability.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20\",\n                                                    style: {\n                                                        animationDelay: `${index * 100}ms`\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-16 h-16 bg-gradient-to-br ${capability.gradient} rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110`,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"w-8 h-8 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                                                children: capability.title\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 leading-relaxed\",\n                                                                children: capability.description\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, capability.title, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/dashboard\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 group border border-blue-300/50 cursor-pointer transform hover:scale-105\",\n                                                    style: {\n                                                        animationDelay: \"400ms\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-8 h-8 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-white mb-4\",\n                                                                children: \"立即体验\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-100 leading-relaxed mb-4\",\n                                                                children: \"点击进入云宇政数平台，开始您的数据管理之旅\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold flex items-center group-hover:translate-x-1 transition-transform\",\n                                                                    children: [\n                                                                        \"进入平台\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-5 h-5 ml-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 209,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"200ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"解决的应用场景\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"覆盖政务管理的各个关键环节，提升政府治理效能\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                        children: scenarios.map((scenario, index)=>{\n                                            const Icon = scenario.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20\",\n                                                style: {\n                                                    animationDelay: `${index * 100}ms`\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                                    children: scenario.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 239,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 leading-relaxed\",\n                                                                    children: scenario.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, scenario.title, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"400ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"数据来源单位\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"覆盖全市24个主要委办局，构建统一的数据生态体系\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6\",\n                                            children: dataSourceUnits.map((unit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 rounded-xl bg-gray-50/50 hover:bg-blue-50/50 transition-all duration-300 group\",\n                                                    style: {\n                                                        animationDelay: `${index * 50}ms`\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-8 h-8 text-gray-400 group-hover:text-blue-500 mx-auto mb-3 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700 group-hover:text-blue-700 transition-colors\",\n                                                            children: unit\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, unit, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"600ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"权威数据覆盖\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"全面覆盖政务核心数据领域，确保数据权威性和完整性\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                        children: authorityData.map((data, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20\",\n                                                style: {\n                                                    animationDelay: `${index * 100}ms`\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-gray-900\",\n                                                                children: data.area\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-3xl font-bold text-blue-600\",\n                                                                children: data.coverage\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-6\",\n                                                        children: data.source\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-gray-200 rounded-full h-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full transition-all duration-1000\",\n                                                            style: {\n                                                                width: data.coverage\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, data.area, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"800ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"服务对象\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"为不同类型用户提供专业化的数据服务和解决方案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                        children: targetUsers.map((user, index)=>{\n                                            const Icon = user.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group text-center border border-white/20\",\n                                                style: {\n                                                    animationDelay: `${index * 100}ms`\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl mx-auto mb-6 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                                                        children: user.type\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-6 leading-relaxed\",\n                                                        children: user.desc\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: user.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-green-500 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    feature\n                                                                ]\n                                                            }, idx, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, user.type, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"1000ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-4 rounded-2xl mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-8 h-8 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                        children: \"业务模块入口\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                children: \"选择您需要的功能模块\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl text-gray-600\",\n                                                children: \"点击下方模块卡片，直接进入对应的业务操作界面\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-3xl p-12 border-2 border-blue-200/50 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                                children: [\n                                                    {\n                                                        name: \"数据大屏\",\n                                                        description: \"实时数据可视化展示，支持多种图表和指标监控\",\n                                                        href: \"/dashboard/screen\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                        gradient: \"from-blue-500 to-cyan-500\",\n                                                        features: [\n                                                            \"实时监控\",\n                                                            \"可视化图表\",\n                                                            \"大屏展示\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"报表分析\",\n                                                        description: \"多维度数据分析报表，支持自定义报表生成\",\n                                                        href: \"/dashboard/reports\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                                        gradient: \"from-green-500 to-emerald-500\",\n                                                        features: [\n                                                            \"多维分析\",\n                                                            \"自定义报表\",\n                                                            \"数据导出\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"数据采集\",\n                                                        description: \"多源数据采集接入，支持实时和批量数据导入\",\n                                                        href: \"/dashboard/collection\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                                                        gradient: \"from-purple-500 to-violet-500\",\n                                                        features: [\n                                                            \"多源接入\",\n                                                            \"实时采集\",\n                                                            \"数据清洗\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"数据汇聚\",\n                                                        description: \"跨系统数据整合汇聚，统一数据标准和格式\",\n                                                        href: \"/dashboard/aggregation\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                                        gradient: \"from-orange-500 to-amber-500\",\n                                                        features: [\n                                                            \"数据整合\",\n                                                            \"标准化\",\n                                                            \"质量控制\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"数据治理\",\n                                                        description: \"数据质量管控，数据清洗、去重、标准化处理\",\n                                                        href: \"/dashboard/governance\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                                        gradient: \"from-red-500 to-rose-500\",\n                                                        features: [\n                                                            \"质量管控\",\n                                                            \"数据清洗\",\n                                                            \"合规管理\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"资源池管理\",\n                                                        description: \"数据资源统一管理，资源目录和权限控制\",\n                                                        href: \"/dashboard/resources\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                                        gradient: \"from-indigo-500 to-blue-500\",\n                                                        features: [\n                                                            \"资源目录\",\n                                                            \"权限管理\",\n                                                            \"存储优化\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"设备监控\",\n                                                        description: \"服务器设备实时监控，性能指标和告警管理\",\n                                                        href: \"/dashboard/monitoring\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                                        gradient: \"from-teal-500 to-cyan-500\",\n                                                        features: [\n                                                            \"实时监控\",\n                                                            \"性能分析\",\n                                                            \"告警管理\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"网络管理\",\n                                                        description: \"多物理网络管理，网络拓扑和流量监控\",\n                                                        href: \"/dashboard/network\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                                                        gradient: \"from-pink-500 to-rose-500\",\n                                                        features: [\n                                                            \"网络拓扑\",\n                                                            \"流量监控\",\n                                                            \"安全管理\"\n                                                        ]\n                                                    }\n                                                ].map((module, index)=>{\n                                                    const Icon = module.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: module.href,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer group transform hover:scale-105 border border-white/50\",\n                                                            style: {\n                                                                animationDelay: `${index * 100}ms`\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative overflow-hidden text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: `absolute inset-0 bg-gradient-to-br ${module.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl`\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative z-10\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: `w-20 h-20 bg-gradient-to-br ${module.gradient} rounded-3xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110`,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                                    className: \"w-10 h-10 text-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 430,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-700 transition-colors\",\n                                                                                children: module.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 text-sm mb-6 leading-relaxed\",\n                                                                                children: module.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 437,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2 mb-6\",\n                                                                                children: module.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-center text-xs text-gray-500\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                                className: \"w-3 h-3 text-green-500 mr-2\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 444,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            feature\n                                                                                        ]\n                                                                                    }, idx, true, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 443,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 441,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-lg font-bold text-blue-600 group-hover:text-blue-700 transition-colors flex items-center\",\n                                                                                    children: [\n                                                                                        \"立即使用\",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            className: \"w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 453,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 451,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 450,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/50 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 460,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, module.name, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mt-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center space-x-3 text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg\",\n                                                            children: \"选择任意模块开始您的数据管理之旅\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"text-center animate-slide-up\",\n                                style: {\n                                    animationDelay: \"1200ms\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-blue-600 to-indigo-600 rounded-3xl p-12 text-white shadow-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold mb-6\",\n                                            children: \"准备好开始了吗？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl mb-12 text-blue-100\",\n                                            children: \"立即体验云宇政数平台，开启您的智慧政务数据管理之旅\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    href: \"/dashboard\",\n                                                    className: \"bg-white text-blue-600 hover:bg-blue-50 font-bold py-5 px-12 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center text-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-6 h-6 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"进入平台\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-blue-500/20 backdrop-blur-sm text-white hover:bg-blue-500/30 font-medium py-5 px-12 rounded-2xl transition-all duration-300 border border-white/20 flex items-center text-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-6 h-6 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"了解更多\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-white/80 backdrop-blur-xl border-t border-white/20 mt-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-6 py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg mb-3\",\n                                        children: \"\\xa9 2024 云宇政数平台. 保留所有权利.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base\",\n                                        children: \"为政府数字化转型提供专业的数据管理服务\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/lucide-react@0.294.0_react@18.3.1","vendor-chunks/@swc+helpers@0.5.5"], () => (__webpack_exec__("(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();